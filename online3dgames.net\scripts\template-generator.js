// Game Template Generator
// This script generates game page templates and recommendation sections automatically

// Import game configuration (in Node.js environment)
const { gameCategories, completeGameData, generateHoneycombLayout, getGamesByCategory, getGamesExcludingCategory } = require('./game-config.js');

// Template for game recommendation card
function generateRecommendationCard(game) {
    return `                <a href="${game.url}" class="recommendation-card" data-category="${game.category}">
                    <div class="card-image">
                        <div class="game-icon">${game.icon}</div>
                        <h4 class="game-name">${game.name}</h4>
                        <div class="game-rating">⭐ ${game.rating}</div>
                    </div>
                </a>`;
}

// Template for honeycomb row
function generateHoneycombRow(games, isOffset = false) {
    const offsetClass = isOffset ? ' offset-row' : '';
    const cards = games.map(game => generateRecommendationCard(game)).join('\n');
    
    return `            <div class="recommendations-row${offsetClass}">
${cards}
            </div>`;
}

// Generate similar games section
function generateSimilarGamesSection(currentGame) {
    const sameCategory = getGamesByCategory(currentGame.category)
        .filter(game => game.url !== currentGame.url)
        .slice(0, 4); // Maximum 4 games for similar section

    // If no same-category games, return empty string
    if (sameCategory.length === 0) {
        return '';
    }

    const categoryInfo = gameCategories[currentGame.category];
    const sectionTitle = currentGame.category === 'strategy' ? 'Strategic Thinking Games' :
                        currentGame.category === 'blackjack' ? 'More Blackjack Games' :
                        `More ${categoryInfo.name}`;

    const subtitle = currentGame.category === 'strategy' ? 'Games that challenge your mind and strategy' :
                    currentGame.category === 'blackjack' ? 'Explore different Blackjack variants' :
                    `Continue exploring ${categoryInfo.name.toLowerCase()}`;

    // Generate honeycomb layout for similar games
    const honeycombRows = generateHoneycombLayout(sameCategory);
    const rowsHtml = honeycombRows.map(row =>
        generateHoneycombRow(row.games, row.isOffset)
    ).join('\n');

    return `    <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">${sectionTitle}</h3>
            <p class="recommendations-subtitle">${subtitle}</p>
        </div>
        <div class="recommendations-grid">
${rowsHtml}
        </div>
    </div>`;
}

// Generate other games section
function generateOtherGamesSection(currentGame, isMainSection = false) {
    const otherGames = getGamesExcludingCategory(currentGame.category)
        .slice(0, 20); // Maximum 20 games for other section to include all remaining games

    // Generate honeycomb layout for other games
    const honeycombRows = generateHoneycombLayout(otherGames);
    const rowsHtml = honeycombRows.map(row =>
        generateHoneycombRow(row.games, row.isOffset)
    ).join('\n');

    // Adjust title and subtitle based on whether this is the main section
    const title = isMainSection ? 'Recommended Games' : 'Other Games';
    const subtitle = isMainSection ? 'Discover amazing games from different categories' : 'Discover more amazing games';

    return `
    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">${title}</h3>
            <p class="recommendations-subtitle">${subtitle}</p>
        </div>
        <div class="recommendations-grid">
${rowsHtml}
        </div>
    </div>`;
}

// Generate complete recommendations section for a game
function generateGameRecommendations(gameKey) {
    const currentGame = completeGameData[gameKey];
    if (!currentGame) {
        console.error(`Game not found: ${gameKey}`);
        return '';
    }

    const similarSection = generateSimilarGamesSection(currentGame);
    const hasSimilarGames = similarSection !== '';
    const otherSection = generateOtherGamesSection(currentGame, !hasSimilarGames);

    // If no similar games section, adjust the other section to be the main recommendations
    if (!hasSimilarGames) {
        return `    <!-- Game Recommendations -->\n${otherSection}`;
    }

    return similarSection + otherSection;
}

// Generate homepage category section
function generateHomepageCategorySection(categoryKey) {
    const category = gameCategories[categoryKey];
    const games = getGamesByCategory(categoryKey);
    
    if (!category || games.length === 0) {
        return '';
    }
    
    // Generate featured game (first game in category)
    const featuredGame = games[0];
    const otherGames = games.slice(1);
    
    let categoryHtml = `            <div class="tab-panel" data-panel="${categoryKey}">
                <div class="category-games-grid">
                    <div class="category-game-card featured">
                        <div class="card-image">
                            <div class="game-icon">${featuredGame.icon}</div>
                        </div>
                        <div class="card-content">
                            <h3 class="game-name">${featuredGame.name}</h3>
                            <p class="game-description">${featuredGame.description}</p>
                            <a href="${featuredGame.url}" class="play-btn" title="${featuredGame.seoTitle}">${featuredGame.name === 'Blackjack Practice' ? 'Practice Now' : 'Play Now'}</a>
                        </div>
                    </div>`;
    
    // Add other games in category
    otherGames.forEach(game => {
        categoryHtml += `

                    <div class="category-game-card">
                        <div class="card-image">
                            <div class="game-icon">${game.icon}</div>
                        </div>
                        <div class="card-content">
                            <h3 class="game-name">${game.name}</h3>
                            <p class="game-description">${game.description}</p>
                            <a href="${game.url}" class="play-btn" title="${game.seoTitle}">${game.name === 'Blackjack Practice' ? 'Practice Now' : 'Play Now'}</a>
                        </div>
                    </div>`;
    });
    
    categoryHtml += `
                </div>
            </div>`;
    
    return categoryHtml;
}

// Generate homepage category tab button
function generateCategoryTabButton(categoryKey) {
    const category = gameCategories[categoryKey];
    const games = getGamesByCategory(categoryKey);
    
    if (!category || games.length === 0) {
        return '';
    }
    
    return `                            <button class="tab-btn" data-tab="${categoryKey}" title="${category.description}">
                                <span class="tab-icon">${category.icon}</span>
                                <span class="tab-text">${category.name}</span>
                                <span class="tab-count">${games.length}</span>
                            </button>`;
}

// Generate sitemap entries for all games
function generateSitemapEntries() {
    const baseUrl = 'https://flowfray.com';
    let sitemapEntries = '';
    
    Object.values(completeGameData).forEach(game => {
        sitemapEntries += `    <url>
        <loc>${baseUrl}${game.url}</loc>
        <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
`;
    });
    
    return sitemapEntries;
}

// Utility function to validate honeycomb layout
function validateHoneycombLayout(games) {
    const layout = generateHoneycombLayout(games);
    let isValid = true;
    let errors = [];
    
    for (let i = 0; i < layout.length - 1; i++) {
        const currentRowSize = layout[i].games.length;
        const nextRowSize = layout[i + 1].games.length;
        const difference = Math.abs(currentRowSize - nextRowSize);
        
        if (difference % 2 === 0) {
            isValid = false;
            errors.push(`Row ${i + 1} (${currentRowSize} games) and Row ${i + 2} (${nextRowSize} games) have even difference (${difference})`);
        }
    }
    
    return { isValid, errors, layout };
}

// Export functions for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateRecommendationCard,
        generateHoneycombRow,
        generateSimilarGamesSection,
        generateOtherGamesSection,
        generateGameRecommendations,
        generateHomepageCategorySection,
        generateCategoryTabButton,
        generateSitemapEntries,
        validateHoneycombLayout
    };
}

// Browser environment functions
if (typeof window !== 'undefined') {
    window.TemplateGenerator = {
        generateRecommendationCard,
        generateHoneycombRow,
        generateSimilarGamesSection,
        generateOtherGamesSection,
        generateGameRecommendations,
        generateHomepageCategorySection,
        generateCategoryTabButton,
        generateSitemapEntries,
        validateHoneycombLayout
    };
}
