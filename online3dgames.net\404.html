<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | Free Online Games</title>
    <meta name="description" content="Oops! The page you're looking for doesn't exist. Discover our amazing collection of free online games instead! Play browser games instantly without downloads.">
    <meta name="keywords" content="404 error, page not found, free games, online games, browser games, web games, HTML5 games">
    <meta name="robots" content="noindex, follow">

    <!-- Open Graph Tags for Social Media -->
    <meta property="og:title" content="404 - Page Not Found | Free Online Games">
    <meta property="og:description" content="Page not found, but discover amazing free online games instead!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://flowfray.com/404">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://flowfray.com/404">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/assets/css/main.css">
    <link rel="stylesheet" href="/assets/css/modal.css">
    <style>
        /* 404 Page Specific Styles */
        .error-section {
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 80px 20px;
            position: relative;
        }

        .error-content {
            background: rgba(26, 26, 26, 0.95);
            border: 1px solid #333;
            border-radius: 20px;
            padding: 60px 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
        }

        .error-number {
            font-size: 8rem;
            font-weight: 700;
            color: #ff6b00;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            line-height: 1;
            background: linear-gradient(45deg, #ff6b00, #ff8c42);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .error-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .error-message {
            font-size: 1.2rem;
            color: #aaa;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 40px;
        }

        .error-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .error-btn-primary {
            background: #ff6b00;
            color: white;
            box-shadow: 0 5px 15px rgba(255, 107, 0, 0.4);
        }

        .error-btn-primary:hover {
            background: #e55a00;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 107, 0, 0.6);
            color: white;
            text-decoration: none;
        }

        .error-btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid #333;
        }

        .error-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            color: white;
            text-decoration: none;
        }

        .popular-games {
            margin-top: 40px;
        }

        .popular-games h3 {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .game-links {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .game-link {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid #333;
        }

        .game-link:hover {
            background: rgba(255, 107, 0, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 0, 0.2);
            color: white;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .error-number { font-size: 6rem; }
            .error-title { font-size: 2rem; }
            .error-message { font-size: 1rem; }
            .error-content { padding: 40px 20px; }
            .action-buttons { flex-direction: column; align-items: center; }
            .error-btn { width: 100%; max-width: 250px; justify-content: center; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Navigation -->
        <nav class="top-nav">
            <div class="nav-container">
                <div class="nav-left">
                    <a href="/" class="logo">
                        <span class="logo-icon">🎰</span>
                        <span class="logo-text">Flow Fray</span>
                    </a>
                </div>
                <div class="nav-center">
                    <div class="search-bar">
                        <input type="text" placeholder="Search games..." class="search-input">
                        <button class="search-btn">🔍</button>
                    </div>
                </div>
                <div class="nav-right">
                    <button class="nav-btn" id="menuToggle">
                        <span class="hamburger"></span>
                        <span class="hamburger"></span>
                        <span class="hamburger"></span>
                    </button>
                </div>
            </div>
        </nav>

        <!-- 404 Error Section -->
        <section class="error-section">
            <div class="error-content">
                <div class="error-number">404</div>
                <h1 class="error-title">Game Over!</h1>
                <p class="error-message">
                    Oops! The page you're looking for seems to have disappeared into the digital void.
                    But don't worry - we have plenty of amazing free online games waiting for you!
                </p>

                <div class="action-buttons">
                    <a href="/" class="error-btn error-btn-primary">
                        🏠 Back to Flow Fray
                    </a>
                    <a href="javascript:history.back()" class="error-btn error-btn-secondary">
                        ↩️ Go Back
                    </a>
                </div>

                <div class="popular-games">
                    <h3>🎮 Try These Popular Free Games</h3>
                    <div class="game-links">
                        <a href="/memoryGame" class="game-link">🧠 Memory Game</a>
                        <a href="/2048" class="game-link">🧩 2048 Puzzle</a>
                        <a href="/snake-game" class="game-link">🐍 Snake Game</a>
                        <a href="/tetris-game" class="game-link">🎲 Tetris</a>
                        <a href="/breakout-game" class="game-link">🎯 Breakout</a>
                        <a href="/texas-holdem-game" class="game-link">♠️ Texas Hold'em</a>
                        <a href="/chess" class="game-link">♔ Chess</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3 class="footer-title">Flow Fray</h3>
                        <p class="footer-description">The ultimate destination for Flow Fray. Master Blackjack, enjoy classic card games, and challenge your mind with puzzles. Play instantly in your browser with no downloads required - experience premium gaming entertainment for free!</p>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-heading">Popular Games</h4>
                        <ul class="footer-links">
                            <li><a href="/blackjack" title="Play Classic Blackjack - Master the art of 21 with professional casino strategies">Classic Blackjack</a></li>
                            <li><a href="/hearts" title="Play Hearts - Strategic trick-taking card game with intelligent AI opponents">Hearts</a></li>
                            <li><a href="/sudoku-game" title="Play Sudoku - Classic logic puzzle for brain training with multiple difficulties">Sudoku</a></li>
                            <li><a href="/tetris-game" title="Play Tetris - Arrange falling blocks to complete lines in this classic puzzle">Tetris</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-heading">Blackjack Collection</h4>
                        <ul class="footer-links">
                            <li><a href="/blackjack" title="Classic Blackjack - Master 21-point casino card game with optimal strategies">Classic Blackjack</a></li>
                            <li><a href="/blackjack-practice" title="Blackjack Practice Mode - Learn optimal strategy and improve your skills risk-free">Blackjack Practice</a></li>
                            <li><a href="/freeBetBlackjack" title="Free Bet Blackjack - Advanced variant with free double downs and splits">Free Bet Blackjack</a></li>
                            <li><a href="/pontoon-game" title="Pontoon - British Blackjack variant with unique rules and terminology">Pontoon Game</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2025 Flow Fray. All rights reserved. Play Flow Fray instantly!</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- jQuery -->
    <script src="/assets/js/jquery-3.7.1.min.js"></script>
    <!-- Main JS -->
    <script src="/assets/js/main.js"></script>

    <script>
        $(document).ready(function() {
            // Add click tracking for analytics
            $('.game-link, .error-btn').on('click', function() {
                const linkText = $(this).text().trim();
                console.log('404 Page - Link clicked:', linkText);
            });

            // Add some interactive effects
            $('.error-number').on('mouseenter', function() {
                $(this).css('transform', 'scale(1.1) rotate(5deg)');
            }).on('mouseleave', function() {
                $(this).css('transform', 'scale(1) rotate(0deg)');
            });

            // Auto-redirect suggestion after 30 seconds
            setTimeout(function() {
                if (confirm('Would you like to return to the main game page?')) {
                    window.location.href = '/';
                }
            }, 30000);

            // Add keyboard navigation
            $(document).keydown(function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    window.location.href = '/';
                } else if (e.key === 'Escape') {
                    history.back();
                }
            });

            // Add search functionality
            const urlParams = new URLSearchParams(window.location.search);
            const searchTerm = urlParams.get('q');
            if (searchTerm) {
                $('.error-message').append('<br><br><strong>Search term:</strong> "' + searchTerm + '"<br>Try browsing our games instead!');
            }

            // Mobile menu toggle
            $('#menuToggle').on('click', function() {
                $(this).toggleClass('active');
            });
        });
    </script>
</body>
</html>
