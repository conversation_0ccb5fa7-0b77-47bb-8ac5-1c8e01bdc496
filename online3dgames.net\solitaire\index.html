<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-49EMLQ4Q49"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-49EMLQ4Q49');
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Klondike Solitaire - Free Classic Card Game | Flow Fray</title>
    <meta name="description" content="Experience the ultimate Klondike Solitaire with stunning 3D flip animations and smooth gameplay. Play the world's most popular card game online for free with beautiful graphics and intuitive controls.">
    <meta name="keywords" content="klondike solitaire, classic solitaire, card game online, patience game, free solitaire, card games, online games, classic games, solitaire cards">
    <meta name="author" content="Flow Fray">
  
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <meta name="theme-color" content="#2d5016">
    
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="/solitaire/css/styles.css" as="style">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/solitaire/css/styles.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">
    
</head>
<body>
    <div id="landscape-prompt" class="landscape-prompt">
        <div class="landscape-content">
            <div class="rotate-icon">📱</div>
            <h2>Better Experience in Landscape</h2>
            <p>Please rotate your device to landscape mode for the best Klondike Solitaire experience</p>
        </div>
    </div>

    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <h1>Klondike Solitaire</h1>
                <div class="game-stats">
                    <div class="stat">
                        <span class="stat-label">Score:</span>
                        <span id="score">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Time:</span>
                        <span id="timer">00:00</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Moves:</span>
                        <span id="moves">0</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <a href="/" class="btn btn-secondary">Home</a>
                <button id="newGameBtn" class="btn btn-primary">New Game</button>
                <button id="undoBtn" class="btn btn-secondary">Undo</button>
                <button id="hintBtn" class="btn btn-secondary">Hint</button>
                <button id="helpBtn" class="btn btn-secondary">Help</button>
                <button id="fullscreenBtn" class="btn btn-secondary">⛶</button>
            </div>
        </header>

        <!-- Game Board -->
        <main class="game-board">
            <!-- Foundation Piles (Ace to King) -->
            <div class="foundation-area">
                <div class="foundation-pile" data-suit="hearts" id="foundation-hearts">
                    <div class="pile-placeholder">♥</div>
                </div>
                <div class="foundation-pile" data-suit="diamonds" id="foundation-diamonds">
                    <div class="pile-placeholder">♦</div>
                </div>
                <div class="foundation-pile" data-suit="clubs" id="foundation-clubs">
                    <div class="pile-placeholder">♣</div>
                </div>
                <div class="foundation-pile" data-suit="spades" id="foundation-spades">
                    <div class="pile-placeholder">♠</div>
                </div>
            </div>

            <!-- Stock and Waste Piles -->
            <div class="stock-area">
                <div class="stock-pile" id="stock">
                    <div class="card-back"></div>
                </div>
                <div class="waste-pile" id="waste"></div>
            </div>

            <!-- Tableau Piles -->
            <div class="tableau-area">
                <div class="tableau-pile" id="tableau-0"></div>
                <div class="tableau-pile" id="tableau-1"></div>
                <div class="tableau-pile" id="tableau-2"></div>
                <div class="tableau-pile" id="tableau-3"></div>
                <div class="tableau-pile" id="tableau-4"></div>
                <div class="tableau-pile" id="tableau-5"></div>
                <div class="tableau-pile" id="tableau-6"></div>
            </div>
        </main>

        <!-- Game Messages -->
        <div id="gameMessage" class="game-message hidden">
            <div class="message-content">
                <h2 id="messageTitle">Congratulations!</h2>
                <p id="messageText">You won the game!</p>
                <div class="message-stats">
                    <div>Score: <span id="finalScore">0</span></div>
                    <div>Time: <span id="finalTime">00:00</span></div>
                    <div>Moves: <span id="finalMoves">0</span></div>
                </div>
                <div class="message-buttons">
                    <button id="playAgainBtn" class="btn btn-primary">Play Again</button>
                    <button id="closeMessageBtn" class="btn btn-secondary">Close</button>
                </div>
            </div>
        </div>



        <!-- Help Panel -->
        <div id="helpPanel" class="help-panel hidden">
            <div class="help-content">
                <div class="help-header">
                    <h3>🃏 Klondike Solitaire</h3>
                    <button id="closeHelpBtn" class="close-btn">×</button>
                </div>

                <div class="help-body">
                    <div class="help-section">
                        <h4>🎯 Game Objective</h4>
                        <p>Move all 52 cards to the four foundation piles, building each suit from Ace to King.</p>
                    </div>

                    <div class="help-section">
                        <h4>📋 Game Rules</h4>
                        <ul>
                            <li><strong>Foundation Piles:</strong> Build up by suit (A, 2, 3... J, Q, K)</li>
                            <li><strong>Tableau Columns:</strong> Build down by alternating colors (red on black, black on red)</li>
                            <li><strong>Empty Columns:</strong> Only Kings can be placed on empty tableau columns</li>
                            <li><strong>Stock Pile:</strong> Click to draw new cards to the waste pile</li>
                            <li><strong>Moving Cards:</strong> Drag cards or click to auto-move</li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h4>⚡ Quick Controls</h4>
                        <ul>
                            <li><strong>Ctrl+N:</strong> Start New Game</li>
                            <li><strong>Ctrl+Z:</strong> Undo Last Move</li>
                            <li><strong>H:</strong> Get Hint</li>
                            <li><strong>Space:</strong> Draw from Stock</li>
                            <li><strong>Esc:</strong> Close Dialogs</li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h4>🏆 Scoring System</h4>
                        <ul>
                            <li>Move card to foundation: <strong>+10 points</strong></li>
                            <li>Flip hidden tableau card: <strong>+5 points</strong></li>
                            <li>Move from foundation back: <strong>-15 points</strong></li>
                        </ul>
                    </div>

                    <div class="help-section challenge-notice">
                        <h4>⚠️ Challenge Level</h4>
                        <p><strong>Difficulty: Random</strong></p>
                        <p>Each game is randomly dealt and may not always be solvable. This classic solitaire game is designed to be challenging - some deals are easier while others require strategic thinking and patience. Don't give up if you get stuck!</p>
                    </div>
                </div>

                <div class="help-footer">
                    <button id="closeHelpBtnBottom" class="btn btn-primary">Start Playing!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section" style="max-width: 1200px; margin: 40px auto; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6; position: relative; z-index: 1; border-radius: 10px;">
        <h2 style="color: #ffffff; margin-bottom: 20px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">Klondike Solitaire - Master the Classic Card Challenge</h2>

        <div style="display: grid;  gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🃏 Classic Solitaire Excellence</h3>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Experience the timeless appeal of Klondike Solitaire with stunning 3D card animations and smooth gameplay. Master the art of strategic card placement in this beloved single-player card game.</p>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Features authentic Klondike rules, beautiful card designs, and intuitive drag-and-drop controls that make every game session enjoyable and engaging.</p>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎯 Game Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li><strong style="color: #ffffff;">3D Card Animations:</strong> Stunning flip and move effects</li>
                    <li><strong style="color: #ffffff;">Drag & Drop:</strong> Intuitive card movement</li>
                    <li><strong style="color: #ffffff;">Auto-Complete:</strong> Smart game finishing</li>
                    <li><strong style="color: #ffffff;">Undo Function:</strong> Reverse your moves</li>
                    <li><strong style="color: #ffffff;">Score Tracking:</strong> Monitor your progress</li>
                </ul>
            </div>
        </div>

        <div style="margin-bottom: 30px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🏆 Strategic Solitaire Mastery</h3>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Develop your strategic thinking and pattern recognition skills as you work to reveal hidden cards and build foundation piles from Ace to King in each suit.</p>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Learn advanced techniques like creating empty tableau columns and managing your stock pile effectively to increase your winning percentage.</p>
        </div>

        <div>
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🌟 The Ultimate Solitaire Experience</h3>
            <p style="margin-bottom: 10px; color: #f0f0f0;">Our Klondike Solitaire combines traditional gameplay with modern visual enhancements, offering the perfect balance of nostalgia and innovation.</p>
            <p style="color: #f0f0f0;">Whether you're a solitaire veteran or new to the game, enjoy hours of strategic card-playing entertainment with this classic patience game!</p>
        </div>
    </div>

                                    <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">More Card Games</h3>
            <p class="recommendations-subtitle">Continue exploring card games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/hearts" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♥️</div>
                        <h4 class="game-name">Hearts</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/spider-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🕷️</div>
                        <h4 class="game-name">Spider Solitaire</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/texas-holdem-game" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🎲</div>
                        <h4 class="game-name">Texas Hold'em</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/spades" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♠️</div>
                        <h4 class="game-name">Spades</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">Other Games</h3>
            <p class="recommendations-subtitle">Discover more amazing games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/2048" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🔢</div>
                        <h4 class="game-name">2048</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎰</div>
                        <h4 class="game-name">Blackjack</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/blackjack-practice" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎯</div>
                        <h4 class="game-name">Blackjack Practice</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/freeBetBlackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🆓</div>
                        <h4 class="game-name">Free Bet Blackjack</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/pontoon-game" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🚤</div>
                        <h4 class="game-name">Pontoon</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/chess" class="recommendation-card" data-category="strategy">
                    <div class="card-image">
                        <div class="game-icon">♔</div>
                        <h4 class="game-name">Chess</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/sudoku-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧩</div>
                        <h4 class="game-name">Sudoku</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/tetris-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🟦</div>
                        <h4 class="game-name">Tetris</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/memoryGame" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧠</div>
                        <h4 class="game-name">Memory Game</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row">
                <a href="/snake-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🐍</div>
                        <h4 class="game-name">Snake</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/breakout-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🧱</div>
                        <h4 class="game-name">Breakout</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/reaction-test" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">⚡</div>
                        <h4 class="game-name">Reaction Test</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/pop-it-game" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🫧</div>
                        <h4 class="game-name">Pop-it</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/fidget-spinner" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌀</div>
                        <h4 class="game-name">Fidget Spinner</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/breathing-ball" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌬️</div>
                        <h4 class="game-name">Breathing Ball</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/drawing-wall" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🎨</div>
                        <h4 class="game-name">Drawing Wall</h4>
                        <div class="game-rating">⭐ 4.2</div>
                    </div>
                </a>
                <a href="/bubble-float" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">💭</div>
                        <h4 class="game-name">Bubble Float</h4>
                        <div class="game-rating">⭐ 4.1</div>
                    </div>
                </a>
                <a href="/particle-trail" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">✨</div>
                        <h4 class="game-name">Particle Trail</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
                <a href="/typing-machine" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">⌨️</div>
                        <h4 class="game-name">Typing Machine</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/virtual-pet" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">🐱</div>
                        <h4 class="game-name">Virtual Pet</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <script src="/assets/js/jquery-3.7.1.min.js"></script>
    <script src="/solitaire/js/game.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>