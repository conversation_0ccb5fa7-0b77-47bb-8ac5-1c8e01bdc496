/* Common Button Styles for Blackjack Games */
/* Unified styles for top-right control buttons */

.top-status {
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    margin: 0 auto;
}

.right-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
}

/* Unified button styles for all top-right control buttons */
.top-control-button,
.home-button,
.fullscreen-button,
.settings-button,
.audio-button,
.rules-button {
    background: linear-gradient(145deg, rgba(79, 70, 229, 0.4), rgba(55, 48, 163, 0.4));
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px;
    font-size: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
    user-select: none;
}

.top-control-button:hover,
.home-button:hover,
.fullscreen-button:hover,
.settings-button:hover,
.audio-button:hover,
.rules-button:hover {
    background: linear-gradient(145deg, #5b52e8, #4338ca);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.top-control-button:active,
.home-button:active,
.fullscreen-button:active,
.settings-button:active,
.audio-button:active,
.rules-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
}

/* Icon styles */
.home-icon,
.fullscreen-icon,
.settings-icon,
.audio-icon,
.rules-icon {
    font-size: 14px;
    line-height: 1;
    color: white;
}

/* Disabled state for settings button */
.settings-button.disabled,
.settings-button:disabled {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.4), rgba(75, 85, 99, 0.4));
    border-color: rgba(156, 163, 175, 0.2);
    color: rgba(156, 163, 175, 0.6);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.settings-button.disabled:hover,
.settings-button:disabled:hover {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.4), rgba(75, 85, 99, 0.4));
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .top-status {
        top: 15px;
        right: 8px;
        gap: 8px;
        margin: 0;
        padding: 0 10px;
    }

    .right-controls {
        gap: 8px;
    }

    .top-control-button,
    .home-button,
    .fullscreen-button,
    .settings-button,
    .audio-button,
    .rules-button {
        min-height: 44px;
        min-width: 44px;
        width: 44px;
        height: 44px;
    }
}

/* Focus styles for accessibility */
.top-control-button:focus,
.home-button:focus,
.fullscreen-button:focus,
.settings-button:focus,
.audio-button:focus,
.rules-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.5);
}
