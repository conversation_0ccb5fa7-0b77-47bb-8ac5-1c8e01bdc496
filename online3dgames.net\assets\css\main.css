
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    max-width: 400px;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: 24px;
    animation: pulse 2s ease-in-out infinite;
    filter: drop-shadow(0 4px 20px rgba(255, 107, 0, 0.5));
}

.loading-text {
    font-size: 2.5rem;
    font-weight: 900;
    color: #ffffff;
    margin-bottom: 12px;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-subtitle {
    font-size: 1.1rem;
    color: #888;
    margin-bottom: 40px;
}

.loading-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ff6b00, #0078f2, #00d4aa);
    border-radius: 2px;
    animation: loading-progress 2s ease-in-out infinite;
}

@keyframes loading-progress {
    0% {
        width: 0%;
        transform: translateX(-100%);
    }

    50% {
        width: 100%;
        transform: translateX(0%);
    }

    100% {
        width: 100%;
        transform: translateX(100%);
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Top Navigation */
.top-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: rgba(10, 10, 10, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
}

.nav-left {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: #ffffff;
}

.logo-icon {
    font-size: 2rem;
    filter: drop-shadow(0 2px 8px rgba(255, 107, 0, 0.5));
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 900;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-center {
    flex: 1;
    max-width: 500px;
    margin: 0 40px;
}

.search-bar {
    position: relative;
    width: 100%;
}

.search-input {
    width: 100%;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 22px;
    padding: 0 50px 0 20px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    border-color: #ff6b00;
    box-shadow: 0 0 0 3px rgba(255, 107, 0, 0.2);
}

.search-input::placeholder {
    color: #888;
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 32px;
    height: 32px;
    background: #ff6b00;
    border: none;
    border-radius: 50%;
    color: #ffffff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #e55a00;
    transform: translateY(-50%) scale(1.1);
}

.nav-right {
    display: flex;
    align-items: center;
}

.nav-btn {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.hamburger {
    width: 20px;
    height: 2px;
    background: #ffffff;
    border-radius: 1px;
    transition: all 0.3s ease;
}

/* Main Content */
.main-content {
    flex: 1;
    padding-top: 70px;
}

/* Page Main Title (SEO H1) */
.page-title-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 100px 24px 60px;
    text-align: center;
    /* background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%); */
}

.page-main-title {
    font-size: 3.5rem;
    font-weight: 900;
    color: #ffffff;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.page-main-subtitle {
    font-size: 1.4rem;
    color: #888;
    margin-bottom: 40px;
    font-weight: 400;
    line-height: 1.5;
}

.page-description {
    max-width: 1000px;
    margin: 0 auto;
    text-align: left;
}

.page-description p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #bbb;
    margin-bottom: 24px;
    text-align: justify;
}

.page-description p:last-child {
    margin-bottom: 0;
}

/* Hero Section */
.hero-section {
    position: relative;
    overflow: hidden;
    padding: 40px 0;
}

.hero-container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

.hero-left {
    flex: 2;
    position: relative;
    max-width: 900px;
    margin-right: 24px;
    height: 700px;
}

.hero-right {
    flex: 1;
    min-width: 400px;
    background: rgba(15, 15, 15, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow-y: auto;
    height: 700px;
}

/* Swiper Styles */
.hero-swiper {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
}

.hero-slide {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.slide-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 1 !important;
    visibility: visible !important;
}

.blackjack-slide .slide-bg {
    background: linear-gradient(135deg, #ff6b00 0%, #ff8500 50%, #ffaa00 100%);
}

.cards-slide .slide-bg {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f87171 100%);
}

.sudoku-slide .slide-bg {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);
}

.practice-slide .slide-bg {
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 50%, #a78bfa 100%);
}

.tetris-slide .slide-bg {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
}

.slide-content {
    position: relative;
    z-index: 3;
    max-width: 600px;
    padding: 50px 60px;
    color: #ffffff;
}

.slide-visual {
    position: absolute;
    right: 90px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
}

/* Game Badge and Content Styles */
.game-badge {
    display: inline-block;
    background: rgba(0, 0, 0, 0.3);
    color: #ffffff;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    margin-bottom: 24px;
    text-transform: uppercase;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-title {
    font-size: 3rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 16px;
    letter-spacing: -0.02em;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.game-subtitle {
    font-size: 1.4rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 24px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.game-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 32px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.game-features {
    display: flex;
    gap: 12px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.feature {
    background: rgba(0, 0, 0, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.feature:hover {
    background: rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.game-actions {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    color: #000000;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 700;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.btn-primary:hover .btn-icon {
    transform: scale(1.2) rotate(10deg);
}

.game-stats {
    display: flex;
    gap: 32px;
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: 900;
    color: #ffffff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 4px;
}

/* Slide Visual Elements */
.game-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.card-stack {
    position: relative;
    width: 200px;
    height: 140px;
}

.card {
    position: absolute;
    width: 80px;
    height: 120px;
    background: #ffffff;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 900;
    color: #000000;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.card-1 {
    top: 0;
    left: 0;
    z-index: 3;
    transform: rotate(-10deg);
}

.card-2 {
    top: 10px;
    left: 40px;
    z-index: 2;
    transform: rotate(0deg);
}

.card-3 {
    top: 20px;
    left: 80px;
    z-index: 1;
    transform: rotate(10deg);
}

.score-display {
    font-size: 4rem;
    font-weight: 900;
    color: #ffffff;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.suits-display {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.suit {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.suit:hover {
    transform: scale(1.2) translateY(-10px);
    background: rgba(255, 255, 255, 0.2);
}

.sudoku-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.sudoku-cell {
    width: 40px;
    height: 40px;
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #1f2937;
    transition: all 0.3s ease;
}

.sudoku-cell:not(:empty) {
    background: #f3f4f6;
    color: #6366f1;
}

.sudoku-cell:hover {
    transform: scale(1.05);
    border-color: #8b5cf6;
    box-shadow: 0 0 8px rgba(139, 92, 246, 0.3);
}

.practice-display {
    display: flex;
    align-items: center;
    gap: 15px;
    justify-content: center;
}

.strategy-card {
    width: 60px;
    height: 80px;
    background: #ffffff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.strategy-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.card-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1f2937;
}

.card-suit {
    font-size: 1.2rem;
    margin-top: -5px;
}

.plus-sign,
.equals-sign {
    font-size: 2rem;
    color: #ffffff;
    font-weight: bold;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.result-display {
    font-size: 2.5rem;
    font-weight: bold;
    color: #fbbf24;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.tetris-board {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
}

.tetris-block {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.t-block {
    background: linear-gradient(45deg, #8b5cf6, #a78bfa);
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

.l-block {
    background: linear-gradient(45deg, #f59e0b, #fbbf24);
    box-shadow: 0 0 15px rgba(245, 158, 11, 0.5);
}

.i-block {
    background: linear-gradient(45deg, #06b6d4, #22d3ee);
    box-shadow: 0 0 15px rgba(6, 182, 212, 0.5);
}

.o-block {
    background: linear-gradient(45deg, #eab308, #facc15);
    box-shadow: 0 0 15px rgba(234, 179, 8, 0.5);
}

.tetris-block:hover {
    transform: scale(1.1) rotate(5deg);
}

/* Swiper Navigation Customization */
.hero-swiper .swiper-button-next,
.hero-swiper .swiper-button-prev {
    color: #ffffff;
    background: rgba(0, 0, 0, 0.3);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    z-index: 10;
}

.hero-swiper .swiper-button-next:hover,
.hero-swiper .swiper-button-prev:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.hero-swiper .swiper-button-next::after,
.hero-swiper .swiper-button-prev::after {
    font-size: 1.2rem;
    font-weight: 900;
}

.hero-swiper .swiper-pagination {
    bottom: 40px;
    z-index: 10;
}

.hero-swiper .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.4);
    opacity: 1;
    transition: all 0.3s ease;
    cursor: pointer;
}

.hero-swiper .swiper-pagination-bullet:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: scale(1.2);
}

.hero-swiper .swiper-pagination-bullet-active {
    background: #ffffff;
    transform: scale(1.3);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Slide transition effects - Ensure proper fade effect */
.hero-swiper .swiper-slide {
    opacity: 0;
    transition: none;
}

.hero-swiper .swiper-slide-active {
    opacity: 1;
}

.hero-swiper .swiper-slide-duplicate-active {
    opacity: 1;
}

.hero-swiper .swiper-slide-prev,
.hero-swiper .swiper-slide-next {
    opacity: 0;
}

/* Hot game item active effect - static for better performance */
.hot-game-item.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 0, 0.08), rgba(255, 133, 0, 0.08));
    border-radius: 16px;
    z-index: -1;
}

/* Hot Games Panel */
.hot-games-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px 32px;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
}

.panel-header {
    margin-bottom: 12px;
}

.panel-title {
    font-size: 1.8rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 8px;
    letter-spacing: -0.02em;
}

.panel-subtitle {
    color: #888;
    font-size: 1rem;
}

.hot-games-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow-y: auto;
    padding: 0 10px;
}

.hot-game-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.hot-game-item:hover:not(.active) {
    background: rgba(255, 107, 0, 0.1);
    border-color: rgba(255, 107, 0, 0.3);
    transform: translateX(8px);
    box-shadow: 0 8px 24px rgba(255, 107, 0, 0.2);
}

.hot-game-item.active {
    background: rgba(255, 107, 0, 0.15);
    border-color: rgba(255, 107, 0, 0.4);
    box-shadow: 0 8px 24px rgba(255, 107, 0, 0.3);
    transform: translateX(8px);
}

.hot-game-item.active:hover {
    background: rgba(255, 107, 0, 0.2);
    border-color: rgba(255, 107, 0, 0.5);
    box-shadow: 0 12px 32px rgba(255, 107, 0, 0.4);
}

.hot-game-image {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.hot-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: linear-gradient(135deg, #ff6b00, #ff8500);
    color: #ffffff;
    font-size: 0.7rem;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 2;
}

.hot-game-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.hot-game-name {
    font-size: 1rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 4px;
    letter-spacing: -0.025em;
}

.hot-game-genre {
    font-size: 0.85rem;
    color: #888;
    margin-bottom: 8px;
}

.hot-game-stats {
    display: flex;
    gap: 12px;
    font-size: 0.8rem;
}

.hot-game-stats .rating {
    color: #fbbf24;
    font-weight: 600;
}

.hot-game-stats .players {
    color: #60a5fa;
    font-weight: 600;
}

/* Categories Section */
.categories-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
}

.categories-section .section-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Categories Section Header */
.categories-section .section-header {
    text-align: center;
    margin-bottom: 60px;
}

.categories-section .section-title {
    font-size: 3rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 16px;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.categories-section .section-subtitle {
    color: #888;
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.category-intro {
    max-width: 800px;
    margin: 0 auto;
}

.category-intro p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #aaa;
    text-align: justify;
}

/* Game Collection Section Header */
.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-container{
    padding: 40px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 12px;
    letter-spacing: -0.02em;
}

.section-subtitle {
    color: #aaa;
    font-size: 1.1rem;
    font-weight: 400;
}

.category-tabs {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 50px;
    flex-wrap: wrap;
    padding: 0 20px;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    color: #bbb;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    white-space: nowrap;
    min-height: 44px;
}

.tab-btn:hover {
    background: rgba(255, 107, 0, 0.15);
    border-color: rgba(255, 107, 0, 0.4);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(255, 107, 0, 0.25);
}

.tab-btn.active {
    background: linear-gradient(135deg, rgba(255, 107, 0, 0.25) 0%, rgba(255, 133, 0, 0.25) 100%);
    border-color: rgba(255, 107, 0, 0.5);
    color: #ffffff;
    box-shadow: 0 6px 25px rgba(255, 107, 0, 0.35);
    transform: translateY(-1px);
}

.tab-icon {
    font-size: 1.1rem;
    min-width: 20px;
    text-align: center;
    opacity: 0.9;
}

.tab-text {
    font-weight: 500;
    letter-spacing: 0.02em;
}

.tab-count {
    background: rgba(255, 107, 0, 0.25);
    color: #ff8500;
    padding: 3px 7px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(255, 107, 0, 0.4);
    min-width: 20px;
    text-align: center;
}

.tab-btn:hover .tab-count {
    background: rgba(255, 107, 0, 0.4);
    color: #ffffff;
    border-color: rgba(255, 107, 0, 0.6);
}

.tab-btn.active .tab-count {
    background: rgba(255, 107, 0, 0.5);
    color: #ffffff;
    border-color: rgba(255, 107, 0, 0.7);
}

.tab-content {
    width: 100%;
}

.tab-panel {
    display: none;
    animation: fadeInUp 0.4s ease-out;
}

.tab-panel.active {
    display: block;
}

/* Category Games Grid */
.category-games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 0;
}

/* Category Game Card - Clean Modern Design */
.category-game-card {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    overflow: hidden;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
    position: relative;
    display: flex;
    flex-direction: column;
}

.category-game-card:hover {
    border-color: #ff6b00;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 0, 0.15);
}

.category-game-card .card-image {
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.category-game-card .game-icon {
    font-size: 3rem;
    color: #ff6b00;
    transition: transform 0.2s ease;
}

.category-game-card:hover .game-icon {
    transform: scale(1.1);
}

.category-game-card .card-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 12px;
}

.category-game-card .game-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    line-height: 1.3;
    text-align: center;
}

.category-game-card .game-description {
    color: #aaa;
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
    text-align: center;
    flex: 1;
}

.category-game-card .play-btn {
    background: #ff6b00;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    text-align: center;
    display: block;
    margin-top: auto;
}

.category-game-card .play-btn:hover {
    background: #e55a00;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 0, 0.3);
    color: white;
    text-decoration: none;
}

/* Mobile Responsive Design for Category Cards */
@media (max-width: 768px) {
    .category-games-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
    }

    .category-game-card .card-image {
        height: 120px;
    }

    .category-game-card .game-icon {
        font-size: 2.5rem;
    }

    .category-game-card .card-content {
        padding: 16px;
        gap: 10px;
    }

    .category-game-card .game-name {
        font-size: 1.1rem;
    }

    .category-game-card .game-description {
        font-size: 0.85rem;
    }

    .category-game-card .play-btn {
        padding: 10px 20px;
        font-size: 0.85rem;
    }

    .categories-section .section-title{
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .category-games-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .category-game-card .card-image {
        height: 100px;
    }

    .category-game-card .game-icon {
        font-size: 2rem;
    }

    .category-game-card .card-content {
        padding: 14px;
        gap: 8px;
    }
}



:root {
    --hex-size: 120px;
}

/* Honeycomb Grid Container */
.games-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 20px;
    max-width: 1000px;
    margin: 0 auto;
}

/* Honeycomb Row */
.honeycomb-row {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: calc(var(--hex-size) * -0.29);
}

/* Last row doesn't need bottom margin */
.honeycomb-row:last-child {
    margin-bottom: 0;
}

/* Hexagon Game Card */
.game-card {
    width: var(--hex-size);
    height: calc(var(--hex-size) * 1.1547);
    position: relative;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
    z-index: 1;
    transition: filter 0.3s ease;
}

/* Hexagon Shape */
.game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--card-primary, #667eea), var(--card-secondary, #764ba2));
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    z-index: -1;
    transition: filter 0.3s ease;
}

/* Hover Effects */
.game-card:hover {
    z-index: 10;
    filter: brightness(1.1);
}

.game-card:hover::before {
    filter: brightness(1.2) saturate(1.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Game Category Colors */
.game-card[data-categories*="blackjack"] {
    --card-primary: #D4AF37;
    --card-secondary: #8B0000;
}

.game-card[data-categories*="card-games"] {
    --card-primary: #228B22;
    --card-secondary: #2F4F4F;
}

.spider-solitaire-bg {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 50%, #1a202c 100%);
}

.game-card[data-categories*="puzzle"] {
    --card-primary: #8A2BE2;
    --card-secondary: #FF6347;
}

.game-card[data-categories*="arcade"] {
    --card-primary: #00CED1;
    --card-secondary: #FF1493;
}

.game-card[data-categories*="relaxation"] {
    --card-primary: #87CEEB;
    --card-secondary: #DDA0DD;
}

/* Game Content */
.card-image {
    position: absolute;
    top: 15%;
    left: 15%;
    width: 70%;
    height: 70%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    z-index: 2;
}

.game-icon {
    font-size: 2.8rem;
    color: #ffffff;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
    transition: transform 0.3s ease;
}

.game-card:hover .game-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Game Name Display in Hexagon */
.games-grid .game-name {
    position: absolute;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    text-align: center;
    white-space: nowrap;
    z-index: 3;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.games-grid .game-card:hover .game-name {
    opacity: 1;
}

/* Hide detailed info in hexagon layout */
.games-grid .card-info .game-description,
.games-grid .game-tags,
.games-grid .game-meta {
    display: none;
}





/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --hex-size: 60px;
    }

    .nav-center{
        display: none;
    }

    .game-icon{
        font-size: 1.4rem;
    }

    .games-grid {
        padding: 20px 0;
    }
}

/* Footer */
.footer {
    background: #0a0a0a;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 60px 0 30px;
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section {
    color: #ccc;
}

.footer-title {
    font-size: 1.5rem;
    font-weight: 900;
    color: #ffffff;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-description {
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-heading {
    font-size: 1.1rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 16px;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ff6b00;
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #888;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-container {
        flex-direction: column;
        height: auto;
        max-width: 100%;
        padding: 0 20px;
    }

    .hero-left {
        height: 47vh;
        min-height: 350px;
        max-width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
        display: none;
    }

    .hero-right {
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px 20px 0 0;
        min-width: auto;
    }

    .slide-content {
        padding: 40px 40px;
    }

    .slide-visual {
        position: relative;
        right: auto;
        top: auto;
        transform: none;
        margin-top: 40px;
        text-align: center;
        display: none;
    }

    .tab-btn{
        padding: 12px 10px;
    }

    .tab-btn .tab-icon{
        display: none;
    }

    .game-title {
        font-size: 3rem;
    }

    .hot-games-panel{
        padding: 20px 10px;
    }

    .category-tabs{
        flex-wrap: nowrap;
        overflow-x: auto;
        margin-bottom: 20px;
        padding: 1px 20px;
        justify-content: start;
    }

    .hot-game-item.active{
        transform: none;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: none;
    }

    /* Page Title Mobile Styles */
    .page-main-title {
        font-size: 2rem;
    }

    .page-main-subtitle {
        font-size: 1rem;
    }

    .page-title-container {
        padding: 50px 15px 30px;
    }

    .page-description p {
        font-size: 1rem;
        text-align: left;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(255, 107, 0, 0.6), rgba(255, 133, 0, 0.6));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(255, 107, 0, 0.8), rgba(255, 133, 0, 0.8));
}

/* Selection Styling */
::selection {
    background: rgba(255, 107, 0, 0.3);
    color: #ffffff;
}

::-moz-selection {
    background: rgba(255, 107, 0, 0.3);
    color: #ffffff;
}

/* Hidden class for filtering */
.hidden {
    display: none !important;
}

/* Game Recommendations Section */
.game-recommendations {
    max-width: 1200px;
    margin: 40px auto;
    padding: 20px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.recommendations-header {
    text-align: center;
    margin-bottom: 30px;
}

.recommendations-title {
    color: #ffffff;
    font-size: 28px;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.recommendations-subtitle {
    color: #cccccc;
    font-size: 16px;
    margin-bottom: 0;
}

.recommendations-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    max-width: 800px;
    margin: 0 auto;
}

.recommendations-row {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: calc(var(--hex-size) * -0.29);
}

.recommendations-row:last-child {
    margin-bottom: 0;
}

.recommendation-card {
    width: calc(var(--hex-size) * 0.8);
    height: calc(var(--hex-size) * 0.8 * 1.1547);
    position: relative;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
    z-index: 1;
    transition: all 0.3s ease;
}

.recommendation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--card-primary, #667eea), var(--card-secondary, #764ba2));
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    z-index: -1;
    transition: all 0.3s ease;
}

.recommendation-card:hover {
    z-index: 10;
    transform: scale(1.1);
}

.recommendation-card:hover::before {
    filter: brightness(1.2) saturate(1.3);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.recommendation-card .card-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.recommendation-card .game-icon {
    font-size: 2rem;
    margin-bottom: 8px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.recommendation-card .game-name {
    font-size: 0.9rem;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    line-height: 1.2;
    margin: 0;
}

.recommendation-card .game-rating {
    font-size: 0.7rem;
    color: #ffeb3b;
    margin-top: 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* Color variations for different game types */
.recommendation-card[data-category="blackjack"] {
    --card-primary: #d32f2f;
    --card-secondary: #f57c00;
}

.recommendation-card[data-category="card-games"] {
    --card-primary: #1976d2;
    --card-secondary: #388e3c;
}

.recommendation-card[data-category="puzzle"] {
    --card-primary: #7b1fa2;
    --card-secondary: #512da8;
}

.recommendation-card[data-category="arcade"] {
    --card-primary: #f57c00;
    --card-secondary: #e64a19;
}

.recommendation-card[data-category="relaxation"] {
    --card-primary: #00796b;
    --card-secondary: #4caf50;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .game-recommendations {
        margin: 20px auto;
        padding: 15px;
    }

    .recommendations-title {
        font-size: 24px;
    }

    .recommendations-grid {
        padding: 15px 0;
    }

    .recommendation-card {
        width: calc(var(--hex-size) * 0.7);
        height: calc(var(--hex-size) * 0.7 * 1.1547);
        margin: 0 5px;
    }

    .recommendation-card .game-icon {
        font-size: 1.5rem;
    }

    .recommendation-card .game-name {
        font-size: 0.8rem;
    }

    .recommendation-card .game-rating {
        font-size: 0.6rem;
    }
}

@media (max-width: 480px) {
    .recommendations-row {
        flex-wrap: wrap;
        justify-content: center;
        margin-bottom: 10px;
    }

    .recommendation-card {
        width: calc(var(--hex-size) * 0.6);
        height: calc(var(--hex-size) * 0.6 * 1.1547);
        margin: 5px;
    }
}